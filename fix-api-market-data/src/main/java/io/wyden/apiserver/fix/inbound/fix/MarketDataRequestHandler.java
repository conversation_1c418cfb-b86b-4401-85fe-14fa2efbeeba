package io.wyden.apiserver.fix.inbound.fix;

import io.wyden.apiserver.fix.common.fix.FixMessageHandler;
import io.wyden.apiserver.fix.common.marketdata.MarketDataAccessDeniedException;
import io.wyden.apiserver.fix.common.marketdata.MarketDataService;
import org.springframework.stereotype.Component;
import quickfix.FieldNotFound;
import quickfix.IncorrectTagValue;
import quickfix.MessageCracker;
import quickfix.SessionID;
import quickfix.field.SubscriptionRequestType;
import quickfix.fix44.MarketDataRequest;

@Component
public class MarketDataRequestHandler implements FixMessageHandler {

    private final MarketDataService marketDataService;

    public MarketDataRequestHandler(MarketDataService marketDataService) {
        this.marketDataService = marketDataService;
    }

    @MessageCracker.Handler
    public void onMessage(MarketDataRequest message, SessionID sessionId) {
        try {
            SubscriptionRequestType subscriptionRequestType = message.getSubscriptionRequestType();
            switch (subscriptionRequestType.getValue()) {
                case SubscriptionRequestType.SNAPSHOT_UPDATES -> marketDataService.subscribeMarketData(sessionId, message);
                case SubscriptionRequestType.DISABLE_PREVIOUS_SNAPSHOT_UPDATE_REQUEST -> marketDataService.unsubscribeMarketData(sessionId, message);
                default -> marketDataService.rejectMessage(sessionId, message, new FieldNotFound("Unknown subscriptionRequestType"));
            }
        } catch (FieldNotFound | IllegalArgumentException | MarketDataAccessDeniedException e) {
            marketDataService.rejectMessage(sessionId, message, e);
        } catch (IncorrectTagValue e) {
            throw new RuntimeException(e);
        }
    }
}
