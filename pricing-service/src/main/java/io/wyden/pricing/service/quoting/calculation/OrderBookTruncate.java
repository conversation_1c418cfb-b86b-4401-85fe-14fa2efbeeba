package io.wyden.pricing.service.quoting.calculation;

import io.wyden.published.marketdata.MarketDataEvent;
import io.wyden.published.marketdata.OrderBook;
import io.wyden.published.marketdata.OrderBookLevel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Comparator;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

public class OrderBookTruncate {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderBookTruncate.class);

    /**
     * @param maximumDepth, in percentage of the book
     */
    public static MarketDataEvent.Builder truncateDepth(MarketDataEvent.Builder event, BigDecimal maximumDepth) {
        if (event.hasOrderBook()) {
            OrderBook inOrderBook = event.getOrderBook();
            Map<String, OrderBookLevel> inAsks = inOrderBook.getAsksMap();
            Map<String, OrderBookLevel> inBids = inOrderBook.getBidsMap();

            Map<String, OrderBookLevel> truncatedAsks = truncateDepth(inAsks, maximumDepth, SortOrder.ASCENDING);
            Map<String, OrderBookLevel> truncatedBids = truncateDepth(inBids, maximumDepth, SortOrder.DESCENDING);

            OrderBook.Builder outBook = inOrderBook.toBuilder();
            outBook.clearBids();
            outBook.clearAsks();

            outBook.putAllAsks(truncatedAsks);
            outBook.putAllBids(truncatedBids);

            event.clearOrderBook();
            event.setOrderBook(outBook);

            return event;

        } else {
            LOGGER.warn("Expected OrderBook event, but got: " + event);
            return event;
        }

    }

    public enum SortOrder {
        ASCENDING,
        DESCENDING
    }

    public static Map<String, OrderBookLevel> truncateDepth(Map<String, OrderBookLevel> input, BigDecimal maximumDepth, SortOrder sortOrder) {
        if (maximumDepth.compareTo(BigDecimal.ZERO) < 0 || maximumDepth.compareTo(BigDecimal.ONE) > 0) {
            throw new IllegalArgumentException("maximumDepth must be between 0 and 1 (inclusive)");
        }

        int totalEntries = input.size();
        int entriesToKeep = maximumDepth.multiply(new BigDecimal(totalEntries)).setScale(0, RoundingMode.UP).intValue();

        // Define a comparator that converts String keys to BigDecimal for comparison
        Comparator<String> comparator = Comparator.comparing(BigDecimal::new);
        if (sortOrder == SortOrder.DESCENDING) {
            comparator = comparator.reversed();
        }

        // Sort entries by key in the specified order
        Map<String, OrderBookLevel> sortedMap = new TreeMap<>(comparator);
        sortedMap.putAll(input);

        // Collect the desired number of entries based on the sorting order
        return sortedMap.entrySet().stream()
            .limit(entriesToKeep)
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                Map.Entry::getValue,
                (e1, e2) -> e1,
                TreeMap::new
            ));
    }
}
