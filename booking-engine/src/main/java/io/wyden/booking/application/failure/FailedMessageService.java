package io.wyden.booking.application.failure;

import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import static org.slf4j.LoggerFactory.getLogger;

@Service
public class FailedMessageService {

    private static final Logger LOGGER = getLogger(FailedMessageService.class);

    private final FailedMessageRepository repository;
    private final boolean enabled;

    public FailedMessageService(FailedMessageRepository repository,
                                @Value("${booking-engine.failed-messages.enabled:false}") boolean enabled) {
        this.repository = repository;
        this.enabled = enabled;
    }

    public void save(Object message, String description, Throwable throwable) {
        if (!enabled) {
            LOGGER.debug("Failed message processing is disabled. Message will not be saved: {}, {}", description, message);
            return;
        }

        String category = message != null ? message.getClass().getSimpleName() : null;
        String textMessage = message != null ? message.toString() : null;
        String reason = throwable != null ? throwable.getMessage() : null;

        try {
            FailedMessage failedMessage = new FailedMessage(
                category,
                textMessage,
                description,
                reason);

            repository.save(failedMessage);
        } catch (Exception e) {
            LOGGER.error("Failed to save FailedMessage. How ironic... " +
                    "category=({}), message=({}), desc=({}), reason=({})",
                category, message, description, reason);
        }
    }

    public void save(Object message, String description) {
        save(message, description, null);
    }
}
