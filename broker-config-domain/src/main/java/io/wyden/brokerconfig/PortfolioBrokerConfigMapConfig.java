package io.wyden.brokerconfig;

import com.hazelcast.config.Config;
import com.hazelcast.config.MapStoreConfig;
import com.hazelcast.config.SerializationConfig;
import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import io.wyden.cloudutils.hazelcast.HazelcastMapConfig;
import io.wyden.published.brokerdesk.PortfolioConfig;
import jakarta.annotation.Nullable;

import static io.wyden.cloudutils.hazelcast.Serializers.protobufSerializer;

public class PortfolioBrokerConfigMapConfig extends HazelcastMapConfig {

    public static final String BROKER_CONFIG_PORTFOLIO_V_0_1 = "broker-config-portfolio_v0.1";

    public static IMap<String, PortfolioConfig> getMap(HazelcastInstance hazelcastInstance) {
        return hazelcastInstance.getMap(BROKER_CONFIG_PORTFOLIO_V_0_1);
    }

    @Override
    public String getMapName() {
        return BROKER_CONFIG_PORTFOLIO_V_0_1;
    }

    @Override
    protected void addSerializersConfig(SerializationConfig serializationConfig) {
        serializationConfig.addSerializerConfig(protobufSerializer(PortfolioConfig.class, PortfolioConfig.parser()));
    }

    @Override
    protected void addMapConfig(Config config, @Nullable MapStoreConfig mapStoreConfig) {
        super.addMapConfig(config, mapStoreConfig);
    }
}
