package io.wyden.apiserver.fix.common.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public final class FixUtils {

    // TODO: This should be sourced from reference data instead!

    private static final Pattern PATTERN = Pattern.compile("^(\\w+)@(\\w+)@(.+)$");

    private FixUtils() {
        // Empty
    }

    public static String getSymbolFromInstrumentId(String instrumentId) {
        Matcher matcher = PATTERN.matcher(instrumentId);
        if(matcher.find()) {
            return matcher.group(1);
        } else
            throw new IllegalArgumentException("Unexpected instrumentId format, cannot find symbol in: " + instrumentId);
    }

    public static String getSecurityTypeFromInstrumentId(String instrumentId) {
        Matcher matcher = PATTERN.matcher(instrumentId);
        if(matcher.find()) {
            return matcher.group(2);
        } else
            throw new IllegalArgumentException("Unexpected instrumentId format, cannot find securityType in: " + instrumentId);
    }

    public static String getVenueFromInstrumentId(String instrumentId) {
        Matcher matcher = PATTERN.matcher(instrumentId);
        if(matcher.find()) {
            return matcher.group(3);
        } else
            throw new IllegalArgumentException("Unexpected instrumentId format, cannot find venue name in: " + instrumentId);
    }
}
