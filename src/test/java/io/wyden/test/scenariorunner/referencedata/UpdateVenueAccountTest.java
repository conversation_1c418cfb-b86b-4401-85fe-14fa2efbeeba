package io.wyden.test.scenariorunner.referencedata;

import io.qameta.allure.Epic;
import io.qameta.allure.Owner;
import io.wyden.apiserver.rest.venueaccount.model.KeyValue;
import io.wyden.apiserver.rest.venueaccount.model.UpdateVenueAccountRequest;
import io.wyden.test.scenariorunner.data.ErrorMsg;
import io.wyden.test.scenariorunner.data.refdata.VenueAccountConstants;
import io.wyden.test.scenariorunner.data.user.Manager;
import io.wyden.test.scenariorunner.extension.alltest.GraphQLActorExtension;
import io.wyden.test.scenariorunner.extension.alltest.SecurityIntegratorExtension;
import io.wyden.test.scenariorunner.extension.alltest.VenueAccountExtension;
import io.wyden.test.scenariorunner.extension.annotation.RequiredServiceUp;
import io.wyden.test.scenariorunner.extension.infra.OemsHealthObserverExtension;
import io.wyden.test.scenariorunner.integration.gqlclient.GraphQLActor;
import io.wyden.test.scenariorunner.integration.service.Service;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.web.client.RestClientException;

import java.util.List;

import static io.wyden.test.scenariorunner.data.infra.Epics.REFERENCE_DATA;
import static io.wyden.test.scenariorunner.data.infra.Owners.DKOUZAN;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;

/**
 * Update of keyValues tested in ConnectorDeploymentTest#whenDeployedConnectorKeysUpdated_thenAccountDetailsContainNewApiKeys
 */
@Epic(REFERENCE_DATA)
@Owner(DKOUZAN)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@ExtendWith({
    OemsHealthObserverExtension.class,
    SecurityIntegratorExtension.class
})
@RequiredServiceUp({
    Service.ACCESS_GATEWAY,
    Service.REST_API_SERVER,
    Service.REFERENCE_DATA,
    Service.TARGET_REGISTRY
})
public class UpdateVenueAccountTest {

    @Order(1)
    @RegisterExtension
    GraphQLActorExtension accountCreatorExtension = new GraphQLActorExtension();
    @Order(2)
    @RegisterExtension
    VenueAccountExtension venueAccountExtension = new VenueAccountExtension(
        accountCreatorExtension.clientId(),
        List.of(VenueAccountConstants.MOCK_VENUE));
    @Order(3)
    @RegisterExtension
    GraphQLActorExtension nonAccountCreatorExtension = new GraphQLActorExtension(Manager.GROUP_NAME, false);

    GraphQLActor accountCreator;

    @BeforeAll
    void setupActors() {
        accountCreator = accountCreatorExtension.actor();
    }

    @Test
    void whenNonAccountCreatorUpdatesAccount_shouldReceiveAccessDenied() {

        List<KeyValue> updatedKeys = List.of(new KeyValue("updated", "updated"));
        UpdateVenueAccountRequest updateRequest = new UpdateVenueAccountRequest(
            venueAccountExtension.venueAccountName(), venueAccountExtension.venueAccountName(), updatedKeys
        );
        assertThatExceptionOfType(RestClientException.class)
            .isThrownBy(() -> nonAccountCreatorExtension.actor().account().update(updateRequest))
            .withMessageContaining(ErrorMsg.ACCESS_DENIED);
    }

}
