import { useSymbolSearch } from '@wyden/features/instrument-search/useSymbolSearch';
import { useSymbolStore } from '@wyden/features/instrument-search/useSymbolStore';
import { useMemo } from 'react';
import { VenueAccountWithVenue, useVenueAccounts } from './useVenueAccounts';
import { VenueAccountNamesPerVenue, VenueType } from '../services/graphql/generated/graphql';
import { useSOR } from '../features/order-form/useSOR';
import { useEntitlements } from '@wyden/features/entitlements/useEntitlements';
import { ENTITLEMENTS } from '@wyden/constants';

export function useSymbol() {
  const { symbols, loading } = useSymbolSearch();
  const { isSOR } = useSOR();
  const {
    symbol: storeSymbol,
    changeSymbol: changeStoreSymbol,
    venue,
    changeVenue,
  } = useSymbolStore();
  const { checkIfEntitled } = useEntitlements();
  const clobHidden = !checkIfEntitled(ENTITLEMENTS['Wyden Exchange']);
  const { getAccountForId } = useVenueAccounts();
  const clientFilter = (el: VenueAccountNamesPerVenue | null) =>
    isSOR ? el?.instrument?.baseInstrument.venueType !== VenueType.Client : true;
  const permittedClobFilter = (el: VenueAccountNamesPerVenue | null) =>
    clobHidden ? el?.instrument?.baseInstrument.venueType !== VenueType.Clob : true;
  const venues = storeSymbol?.venues?.filter(permittedClobFilter) ?? [];
  const venueNames = venues.filter(clientFilter).map((venue) => venue?.venue) ?? [];
  const symbolVenueAccounts = useMemo(
    () =>
      venues.filter(clientFilter).flatMap(
        (accountNamesPerVenue) =>
          accountNamesPerVenue?.venueAccountDescs
            ?.map((venueAccountDesc) => {
              const accountWithVenue = venueAccountDesc?.id && getAccountForId(venueAccountDesc.id);
              return (
                accountWithVenue ||
                ({
                  venue: accountNamesPerVenue.venue,
                  venueAccountName: venueAccountDesc?.name,
                  venueAccountId: venueAccountDesc?.id,
                } as VenueAccountWithVenue)
              );
            })
            .filter((account) => !account['deactivatedAt']),
      ) ?? [],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [storeSymbol, isSOR],
  );

  return {
    symbol: storeSymbol || symbols[0],
    loading,
    selectedVenue: venue,
    setSelectedVenue: changeVenue,
    venueNames,
    symbolVenues: venues,
    symbolVenueAccounts,
    changeSymbol: changeStoreSymbol,
  };
}
