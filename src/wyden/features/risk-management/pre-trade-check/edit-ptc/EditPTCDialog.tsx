import { Dialog } from '@ui/Dialog';
import { useTranslation } from 'react-i18next';
import { usePreTradeCheckStore } from '@wyden/features/risk-management/pre-trade-check/usePreTradeCheckStore';
import {
  BasicPTCFormSchema,
  PTCForm,
} from '@wyden/features/risk-management/pre-trade-check/schema-adapter/AddPTCForm';
import {
  PreTradeCheckInput,
  PreTradeCheckLevel,
  PreTradeCheckProperty,
  PreTradeCheckPropertyType,
  usePreTradeCheckFormSchemaQuery,
  usePreTradeChecksQuery,
  useSavePreTradeCheckMutation,
} from '@wyden/services/graphql/generated/graphql';
import { Label } from '@ui/Typography/Label';
import {
  Notification,
  useNotification,
} from '@wyden/features/error-indicators/notification/useNotification';
import { usePortfolioTags } from '@wyden/features/focus/usePortfolioTags';
import { styled } from '@ui/styled';
import { color } from '@ui/theme/colors';
import { Alert } from '@ui/Alert';
import { usePtcSchemaToZodAdapter } from '@wyden/features/risk-management/pre-trade-check/schema-adapter/ptcSchemaToZodAdapter';
import startCase from 'lodash/startCase';

export const PRE_TRADE_CHECK_MODAL_TEST_ID = 'pre-trade-check-modal';

const getConfigurationDefaultValues = (configuration: PreTradeCheckProperty[] = []) => {
  return configuration.reduce((acc, value) => {
    const formValue = {
      [PreTradeCheckPropertyType.String]: value.values[0],
      [PreTradeCheckPropertyType.Number]: Number(value.values[0]),
      [PreTradeCheckPropertyType.StringList]: value.values,
      [PreTradeCheckPropertyType.DateTime]: value.values,
    }[value.type];

    return {
      ...acc,
      [value.name]: formValue,
    };
  }, {});
};

export const EditPTCDialogContent = () => {
  const { t } = useTranslation();
  const { preTradeCheckToEdit, selectedCheckType, clearPreTradeCheckToEdit } =
    usePreTradeCheckStore();
  const { data } = usePreTradeCheckFormSchemaQuery();
  const { refetch } = usePreTradeChecksQuery();

  const [savePreTradeCheck, { error, loading, reset }] = useSavePreTradeCheckMutation();
  const { addMessage } = useNotification();
  const { tags } = usePortfolioTags('network-only');
  const tagsWithID = tags.map((tag) => ({
    ...tag,
    id: `${tag.key}: ${tag.value}`,
  }));
  const checkTypeOptions = data?.preTradeCheckFormSchema.map((value) => ({
    id: value.type,
    label: startCase(value.type),
  }));
  const ptcSchema =
    data?.preTradeCheckFormSchema.find((value) => value.type === selectedCheckType)
      ?.configuration || [];
  const { generatedConfig, generatedProps } = usePtcSchemaToZodAdapter(
    ptcSchema,
    selectedCheckType,
  );

  const handleClose = () => {
    clearPreTradeCheckToEdit();
    reset();
  };

  return (
    <Dialog
      data-testid={PRE_TRADE_CHECK_MODAL_TEST_ID}
      open={!!preTradeCheckToEdit}
      onClose={handleClose}
    >
      <PTCForm
        formProps={{
          loading,
          update: true,
          onClose: handleClose,
        }}
        schema={BasicPTCFormSchema.extend(generatedConfig)}
        defaultValues={{
          id: preTradeCheckToEdit?.id,
          checkType: preTradeCheckToEdit?.type,
          checkLevel: preTradeCheckToEdit?.level,
          portfolio: preTradeCheckToEdit?.portfolios,
          channels: preTradeCheckToEdit?.channels,
          portfolioTags: preTradeCheckToEdit?.portfolioTags.map(
            (tag) => `${tag.key}: ${tag.value}`,
          ),
          ...getConfigurationDefaultValues(preTradeCheckToEdit?.configuration),
        }}
        props={{
          id: {
            label: t('common.id'),
            disabled: true,
          },
          checkType: {
            label: t('riskManagement.addPTCForm.checkType'),
            options: checkTypeOptions || [],
          },
          checkLevel: {
            label: t('riskManagement.addPTCForm.checkLevel'),
            options: [
              {
                id: PreTradeCheckLevel.Warn,
                label: 'Warning',
              },
              {
                id: PreTradeCheckLevel.Block,
                label: 'Block',
              },
            ],
            disableRipple: true,
            align: 'equal',
          },
          portfolioTags: {
            multiple: true,
            label: t('riskManagement.addPTCForm.portfolioTags'),
            options: tagsWithID.map((tag) => tag.id),
            freeSolo: false,
          },
          ...generatedProps,
        }}
        onSubmit={(values) => {
          const { id, checkType, checkLevel, portfolio, portfolioTags, channels, ...rest } = values;
          const request: PreTradeCheckInput = {
            type: checkType,
            level: checkLevel,
            portfolios: portfolio ? portfolio.map((p) => p.id) : [],
            portfolioTags: tagsWithID
              .filter((tag) => portfolioTags?.includes(tag.id))
              .map((tag) => ({
                key: tag.key,
                value: tag.value,
              })),
            channels: channels || [],
            id: id,
            configuration: Object.entries(rest)
              .filter(
                ([, value]) =>
                  value !== undefined &&
                  value !== null &&
                  (!Array.isArray(value) || value.length > 0),
              )
              .map(([key, value]) => ({
                name: key,
                values: Array.isArray(value)
                  ? value.map((v) => (typeof v === 'string' ? v : String(v)))
                  : [String(value)],
                type:
                  data?.preTradeCheckFormSchema
                    .find((value) => value.type === checkType)
                    ?.configuration?.find((value) => value.name === key)?.type ||
                  PreTradeCheckPropertyType.String,
              })),
          };
          savePreTradeCheck({
            variables: {
              request,
            },
          })
            .then(() => {
              clearPreTradeCheckToEdit();
              addMessage(Notification.SUCCESS, t('riskManagement.editPTCForm.preMessage'));
              return refetch();
            })
            .catch((err) => {
              const isInternalError =
                err.graphQLErrors[0]?.extensions?.classification === 'INTERNAL_ERROR';
              if (isInternalError) {
                clearPreTradeCheckToEdit();
                addMessage(Notification.ERROR, t('riskManagement.editPTCForm.preMessage'));
              }
            });
        }}
      >
        {({ id, checkType, checkLevel, portfolio, portfolioTags, channels, ...rest }) => {
          return (
            <>
              {id}
              {checkType}
              {checkLevel}
              {portfolio}
              {portfolioTags}
              {channels}
              {selectedCheckType && (
                <ConfigurationLabel variant="small">
                  {t('riskManagement.addPTCForm.configuration')}
                </ConfigurationLabel>
              )}
              {Object.values(rest)}
              {error && <Alert severity="error">{error?.message}</Alert>}
            </>
          );
        }}
      </PTCForm>
    </Dialog>
  );
};

const ConfigurationLabel = styled(Label)`
  font-size: 11px;
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextWeak};
`;
