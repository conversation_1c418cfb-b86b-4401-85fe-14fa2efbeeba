import { Tooltip, TooltipProps, tooltipClasses } from '@mui/material';
import { styled } from '@ui/styled';
import { color } from '@ui/theme/colors';
import { HealthStatus, TargetCapabilityStatus } from '@wyden/services/graphql/generated/graphql';
import format from 'date-fns/format';
import { TFunction } from 'i18next';
import { startCase } from 'lodash';
import { useTranslation } from 'react-i18next';
import { DATE_FORMATS } from '../../date';
import { HealthStatusIcon } from './AccountWithHealthStatusRenderer';
import { getSpacing } from '../../utils/styles';

const TooltipContent = styled('div')`
  display: flex;
  min-width: 370px;
  flex-direction: column;
  padding: 4px 4px 0 4px;
  font-size: 12px;
  font-weight: 400;
`;

const TooltipContentRow = styled('div')`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-content: center;
  align-items: center;
  margin-bottom: 10px;
  gap: ${getSpacing(1)};
`;

const TooltipContentRowLeftSection = styled('div')`
  display: flex;
  align-content: center;
  justify-content: flex-start;
  align-items: center;
  gap: ${getSpacing(1)};
`;

const TooltipContentRowRightSection = styled('div')`
  text-align: right;
  color: ${({ theme }) => color[theme.palette.mode].textElementsTextSecondary};
`;

export const NoMaxWidthTooltip = styled(({ className, ...props }: TooltipProps) => (
  <Tooltip {...props} classes={{ popper: className }} />
))({
  [`& .${tooltipClasses.tooltip}`]: {
    maxWidth: 'none',
  },
});

export const getMessage = (targetState: TargetCapabilityStatus | null, t: TFunction) => {
  targetState?.timestamp ?? console.log(targetState?.timestamp, targetState);
  if (targetState?.healthStatus === HealthStatus.Alive) {
    return `(${t('venueAccounts.operational')})`;
  }

  if (targetState?.healthStatus === HealthStatus.HealthStatusUnspecified) {
    return targetState?.message ?? '-';
  }
  return `${targetState?.message} ${t('venueAccounts.since')}: ${
    targetState?.timestamp
      ? format(new Date(targetState.timestamp), DATE_FORMATS.DEFAULT_WITH_TIME_AND_TZ)
      : '-'
  }`;
};

export const TooltipContentTitle = (props: {
  targetStates: (TargetCapabilityStatus | null)[];
  showCapability?: boolean;
}) => {
  const { t } = useTranslation();

  return (
    <>
      {props.targetStates?.map((targetState, i) => (
        <TooltipContent key={i}>
          <TooltipContentRow>
            {props.showCapability && (
              <TooltipContentRowLeftSection>
                <HealthStatusIcon status={targetState?.healthStatus} showAlive />{' '}
                <div>{startCase(targetState?.capability)}</div>
              </TooltipContentRowLeftSection>
            )}
            <TooltipContentRowRightSection>
              {getMessage(targetState, t)}
            </TooltipContentRowRightSection>
          </TooltipContentRow>
        </TooltipContent>
      ))}
    </>
  );
};
