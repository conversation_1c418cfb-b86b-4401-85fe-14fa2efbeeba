import { HedgePO } from './HedgePO.cy';

describe('<Hedge />', () => {
  const PO = new HedgePO();

  it.skip('Should render Hedge with the proper info', () => {
    PO.render();

    PO.expectTextToBeVisible('Hedge');
    PO.expectTextToBeVisible('(Order: 8dhw74hssj)');
    PO.expectTextToBeVisible('BTC 2.00000000');
    PO.expectTextToBeVisible('for');
    PO.expectTextToBeVisible('127,906.44');

    PO.expectTextToBeVisible('TRY 3,583,938.44');
    PO.expectTextToBeVisible('@');
    PO.expectTextToBeVisible('USD 63,953.22');
    PO.expectTextToBeVisible('TRY 1,791,969.22');

    PO.expectTextToBeVisible('<PERSON>raken');
    PO.expectTextToBeVisible('BUY');
    PO.expectTextToBeVisible('USDTRY');
    PO.expectTextToBeVisible('28.02');
    PO.expectTextToBeVisible('Break-even');
    PO.expectTextToBeVisible('28.15');
  });
  it.skip('Should render Hedge with the table', () => {
    PO.render();

    PO.expectTextToBeVisible('Top-of-book Price');
    PO.expectTextToBeVisible('USD 63,912.31');
    PO.expectTextToBeVisible('TRY 1,790,822.93');

    PO.expectTextToBeVisible('Estimated Price');
    PO.expectTextToBeVisible('USD 63,943');
    PO.expectTextToBeVisible('TRY 1,791,682.86');

    PO.expectTextToBeVisible('Avg Execution Price');
    PO.expectTextToBeVisible('USD 63,953.22');
    PO.expectTextToBeVisible('TRY 1,791,969.22');

    PO.expectTextToBeVisible('Limit Price');
    PO.expectTextToBeVisible('USD 64,882.23');
    PO.expectTextToBeVisible('TRY 1,818,000.08');
  });
});
