package io.wyden.test.scenariorunner.integration.restclient;

import io.wyden.apiserver.rest.apiui.SharedModel;
import io.wyden.apiserver.rest.referencedata.instruments.model.dto.AssetClassDto;
import io.wyden.apiserver.rest.trading.model.CancelRejectResponse;
import io.wyden.apiserver.rest.trading.model.ExecutionReportResponse;
import io.wyden.apiserver.rest.trading.model.NewOrderSingleRequest;
import io.wyden.cloudutils.tools.DateUtils;
import io.wyden.published.client.ClientCancelRejectResponseTo;
import io.wyden.published.client.ClientExecType;
import io.wyden.published.client.ClientInstrumentType;
import io.wyden.published.client.ClientOrderStatus;
import io.wyden.published.client.ClientOrderType;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.client.ClientSide;
import io.wyden.published.client.ClientTIF;
import io.wyden.published.common.Metadata;
import io.wyden.test.scenariorunner.model.trading.NewOrderSingleRequestBuilder;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

public class RestOrderMessageMapper {

    public static NewOrderSingleRequest convertToRest(ClientRequest newOrderSingle) {
        NewOrderSingleRequestBuilder builder = new NewOrderSingleRequestBuilder()
            .setClOrderId(newOrderSingle.getClOrderId())
            .setInstrumentId(newOrderSingle.getInstrumentId())
            .setSymbol(newOrderSingle.getSymbol())
            .setAssetClass(asAssetClass(newOrderSingle.getInstrumentType()))
            .setOrderType(convertToRest(newOrderSingle.getOrderType()))
            .setSide(convertToRest(newOrderSingle.getSide()))
            .setQuantity(BigDecimal.valueOf(Double.parseDouble(newOrderSingle.getQuantity())))
            .setTif(convertToRest(newOrderSingle.getTif()))
            .setPortfolioId(newOrderSingle.getPortfolioId())
            .setVenueAccounts(newOrderSingle.getVenueAccountsList());

        if (!newOrderSingle.getPrice().isBlank()) {
            builder.setLimitPrice(BigDecimal.valueOf(Double.parseDouble(newOrderSingle.getPrice())));
        }
        if (!newOrderSingle.getStopPrice().isBlank()) {
            builder.setStopPrice(BigDecimal.valueOf(Double.parseDouble(newOrderSingle.getStopPrice())));
        }
        if (!newOrderSingle.getExpireTime().isBlank()) {
            ZonedDateTime expireDateTime = DateUtils.fromFixUtcTime(newOrderSingle.getExpireTime());
            String expireTimeEpoch = DateUtils.zonedDateTimeToEpochMillis(expireDateTime);
            builder.setExpirationDateTime(expireTimeEpoch);
        }

        return builder.build();
    }

    private static AssetClassDto asAssetClass(ClientInstrumentType instrumentType) {
        if (instrumentType == null || instrumentType == ClientInstrumentType.UNRECOGNIZED || instrumentType == ClientInstrumentType.INSTRUMENT_TYPE_UNDETERMINED) {
            // should be removed after more instrument types are supported
            return AssetClassDto.FOREX;
        }

        if (instrumentType == ClientInstrumentType.FOREX) {
            return AssetClassDto.FOREX;
        } else {
            throw new UnsupportedOperationException("unsupported instrument type: " + instrumentType);
        }
    }

    public static SharedModel.OrderType convertToRest(ClientOrderType orderType) {
        return switch (orderType) {
            case ORDER_TYPE_UNSPECIFIED -> SharedModel.OrderType.ORDER_TYPE_UNSPECIFIED;
            case STOP_LIMIT -> SharedModel.OrderType.STOP_LIMIT;
            case STOP -> SharedModel.OrderType.STOP;
            case LIMIT -> SharedModel.OrderType.LIMIT;
            case MARKET -> SharedModel.OrderType.MARKET;
            default -> throw new IllegalArgumentException("OrderType " + orderType + " is not supported yet");
        };
    }

    public static SharedModel.Side convertToRest(ClientSide side) {
        return switch (side) {
            case BUY -> SharedModel.Side.BUY;
            case SELL -> SharedModel.Side.SELL;
            case SELL_SHORT -> SharedModel.Side.SELL_SHORT;
            case REDUCE_SHORT -> SharedModel.Side.REDUCE_SHORT;
            default -> throw new IllegalArgumentException("Side " + side + " is not supported yet");
        };
    }

    public static SharedModel.TIF convertToRest(ClientTIF tif) {
        return switch (tif) {
            case DAY -> SharedModel.TIF.DAY;
            case GTC -> SharedModel.TIF.GTC;
            case GTD -> SharedModel.TIF.GTD;
            case IOC -> SharedModel.TIF.IOC;
            case FOK -> SharedModel.TIF.FOK;
            default -> throw new IllegalArgumentException("TIF " + tif + " is not supported yet");
        };
    }

    public static ClientCancelRejectResponseTo convertFromRest(CancelRejectResponse.CancelRejectResponseTo responseTo) {
        return switch (responseTo) {
            case UNSPECIFIED -> ClientCancelRejectResponseTo.CANCEL_REJECT_RESPONSE_TO_UNSPECIFIED;
            case ORDER_CANCEL_REQUEST -> ClientCancelRejectResponseTo.ORDER_CANCEL_REQUEST;
            case ORDER_CANCEL_REPLACE_REQUEST -> ClientCancelRejectResponseTo.ORDER_CANCEL_REPLACE_REQUEST;
        };
    }

    public static ClientResponse toClientResponse(CancelRejectResponse response) {
        return ClientResponse.newBuilder()
            .setClientId(response.getClientId())
            .setClOrderId(response.getClOrderId())
            .setOrigOrderId(response.getOrigOrderId())
            .setOrigClOrderId(response.getOrigClOrderId())
            .setOrderStatus(ClientOrderStatus.valueOf(response.getOrderStatus().name()))
            .setReason(response.getCancelRejectReason())
            .setCancelRejectResponseTo(convertFromRest(response.getCancelRejectResponseTo()))
            .build();
    }

    public static ClientResponse toClientResponse(ExecutionReportResponse response) {
        return ClientResponse.newBuilder()
            .setOrderId(response.getOrderId())
            .setExecutionId(response.getExecutionId())
            .setVenueExecutionId(toNonNullStringValue(response.getVenueExecutionId()))
            .setClOrderId(response.getClOrderId())
            .setOrigClOrderId(response.getOrigClOrderId())
            .setClientId(response.getClientId())
            .setMetadata(Metadata.newBuilder()
                .setRequestId(response.getOrderStatusRequestId())
                .build())
            .setTarget(response.getTargetVenueAccount())
            .setExecType(fromProto(response.getExecType()))
            .setOrderStatus(fromProto(response.getOrderStatus()))
            .setReason(response.getReason())
            .setSide(ClientSide.valueOf(response.getSide().name()))
            .setOrderQty(response.getOrderQty().toPlainString())
            .setLastQty(response.getLastQty().toPlainString())
            .setLeavesQty(response.getLeavesQty().toPlainString())
            .setCumQty(response.getCumQty().toPlainString())
            .setLastPrice(response.getLastPrice())
            .setAvgPrice(response.getAvgPrice())
            .setTimestamp(toNonNullStringValue(response.getTimestamp()))
            .setTargetVenueTimestamp(toNonNullStringValue(response.getTargetVenueTimestamp()))
            .setSymbol(toNonNullStringValue(response.getSymbol()))
            .setInstrumentType(ClientInstrumentType.valueOf(response.getInstrumentType().name()))
            .setVenueAccount(toNonNullStringValue(response.getVenueAccount()))
            .setInstrumentId(response.getInstrumentId())
            .setTargetVenueTicker(response.getTargetVenueTicker())
            .setText(response.getText())
            .setFee(toNonNullStringValue(response.getFee()))
            .setFeeCurrency(toNonNullStringValue(response.getFeeCurrency()))
            .setPortfolioId(response.getPortfolioId())
            .build();
    }

    private static String toNonNullStringValue(BigDecimal value) {
        return value == null ? BigDecimal.ZERO.toString() : String.valueOf(value);
    }

    private static String toNonNullStringValue(String value) {
        return value == null ? StringUtils.EMPTY : value;
    }

    private static ClientOrderStatus fromProto(ExecutionReportResponse.OrderStatus orderStatus) {
        return switch (orderStatus) {
            case ORDER_STATUS_UNSPECIFIED -> ClientOrderStatus.ORDER_STATUS_UNSPECIFIED;
            case NEW -> ClientOrderStatus.NEW;
            case PARTIALLY_FILLED -> ClientOrderStatus.PARTIALLY_FILLED;
            case FILLED -> ClientOrderStatus.FILLED;
            case DONE_FOR_DAY -> ClientOrderStatus.DONE_FOR_DAY;
            case CANCELED -> ClientOrderStatus.CANCELED;
            case REPLACED -> ClientOrderStatus.REPLACED;
            case PENDING_CANCEL -> ClientOrderStatus.PENDING_CANCEL;
            case STOPPED -> ClientOrderStatus.STOPPED;
            case REJECTED -> ClientOrderStatus.REJECTED;
            case SUSPENDED -> ClientOrderStatus.SUSPENDED;
            case PENDING_NEW -> ClientOrderStatus.PENDING_NEW;
            case CALCULATED -> ClientOrderStatus.CALCULATED;
            case EXPIRED -> ClientOrderStatus.EXPIRED;
            case ACCEPTED_FOR_BIDDING -> ClientOrderStatus.ACCEPTED_FOR_BIDDING;
            case PENDING_REPLACE -> ClientOrderStatus.PENDING_REPLACE;
        };
    }

    private static ClientExecType fromProto(ExecutionReportResponse.ExecType execType) {
        return switch (execType) {
            case UNSPECIFIED -> ClientExecType.CLIENT_EXEC_TYPE_UNSPECIFIED;
            case NEW -> ClientExecType.CLIENT_EXEC_TYPE_NEW;
            case PARTIAL_FILL -> ClientExecType.CLIENT_EXEC_TYPE_PARTIAL_FILL;
            case FILL -> ClientExecType.CLIENT_EXEC_TYPE_FILL;
            case DONE_FOR_DAY -> ClientExecType.CLIENT_EXEC_TYPE_DONE_FOR_DAY;
            case CANCELED -> ClientExecType.CLIENT_EXEC_TYPE_CANCELED;
            case REPLACED -> ClientExecType.CLIENT_EXEC_TYPE_REPLACED;
            case PENDING_CANCEL -> ClientExecType.CLIENT_EXEC_TYPE_PENDING_CANCEL;
            case STOPPED -> ClientExecType.CLIENT_EXEC_TYPE_STOPPED;
            case REJECTED -> ClientExecType.CLIENT_EXEC_TYPE_REJECTED;
            case SUSPENDED -> ClientExecType.CLIENT_EXEC_TYPE_SUSPENDED;
            case PENDING_NEW -> ClientExecType.CLIENT_EXEC_TYPE_PENDING_NEW;
            case CALCULATED -> ClientExecType.CLIENT_EXEC_TYPE_CALCULATED;
            case EXPIRED -> ClientExecType.CLIENT_EXEC_TYPE_EXPIRED;
            case RESTATED -> ClientExecType.CLIENT_EXEC_TYPE_RESTATED;
            case PENDING_REPLACE -> ClientExecType.CLIENT_EXEC_TYPE_PENDING_REPLACE;
            case TRADE -> ClientExecType.CLIENT_EXEC_TYPE_TRADE;
            case TRADE_CORRECT -> ClientExecType.CLIENT_EXEC_TYPE_TRADE_CORRECT;
            case TRADE_CANCEL -> ClientExecType.CLIENT_EXEC_TYPE_TRADE_CANCEL;
            case ORDER_STATUS -> ClientExecType.CLIENT_EXEC_TYPE_ORDER_STATUS;
            case TRADE_IN_A_CLEARING_HOLD -> ClientExecType.CLIENT_EXEC_TYPE_TRADE_IN_A_CLEARING_HOLD;
            case TRADE_HAS_BEEN_RELEASED_TO_CLEARING -> ClientExecType.CLIENT_EXEC_TYPE_TRADE_HAS_BEEN_RELEASED_TO_CLEARING;
            case TRIGGERED_OR_ACTIVATED_BY_SYSTEM -> ClientExecType.CLIENT_EXEC_TYPE_TRIGGERED_OR_ACTIVATED_BY_SYSTEM;
        };
    }

}
