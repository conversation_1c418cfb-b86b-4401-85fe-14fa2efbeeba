package io.wyden.oems.storage;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import io.wyden.brokerconfig.PortfolioGroupBrokerConfigMapConfig;
import io.wyden.oems.storage.infra.DatabaseSetupExtension;
import io.wyden.oems.storage.infra.IntegrationTestBase;
import io.wyden.oems.storage.infra.OracleSetupExtension;
import io.wyden.oems.storage.infra.PostgreSQLSetupExtension;
import io.wyden.published.brokerdesk.ExecutionConfig;
import io.wyden.published.brokerdesk.HedgingConfig;
import io.wyden.published.brokerdesk.InstrumentConfig;
import io.wyden.published.brokerdesk.PortfolioGroupConfig;
import io.wyden.published.brokerdesk.PricingConfig;
import io.wyden.published.brokerdesk.TernaryBool;
import io.wyden.published.brokerdesk.ThresholdConfig;
import io.wyden.published.marketdata.InstrumentKey;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.DirtiesContext;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.annotation.DirtiesContext.ClassMode.BEFORE_CLASS;

abstract class BrokerDeskPortfolioGroupConfigPersistenceTest extends IntegrationTestBase {

    public static final String TABLE_NAME = "bcs_portfolio_group_configs";

    public static final String PORTFOLIO_ID = "RetailClient_1";

    static DatabaseSetupExtension DB;

    @Autowired
    private HazelcastInstance hazelcastInstance;
    private IMap<String, PortfolioGroupConfig> hzMap;

    @BeforeEach
    void setUp() {
        hzMap = PortfolioGroupBrokerConfigMapConfig.getMap(hazelcastInstance);
        hzMap.evictAll();
    }

    @Test
    void shouldPersistEvictFetchAndDelete() throws Exception {
        PortfolioGroupConfig saved = config();
        hzMap.put(PORTFOLIO_ID, saved);

        DB.awaitExpectedRecordsInTable(TABLE_NAME, 1);
        DB.logTableContent(TABLE_NAME);

        hzMap.evictAll();
        assertThat(hzMap.size()).isEqualTo(0);
        PortfolioGroupConfig fetched = hzMap.get(PORTFOLIO_ID);
        assertThat(fetched).isEqualTo(saved);

        hzMap.delete(PORTFOLIO_ID);
        DB.awaitExpectedRecordsInTable(TABLE_NAME, 0);
        assertThat(hzMap.size()).isEqualTo(0);
    }

    @Test
    void shouldPersistAndUpdate() throws Exception {
        // insert
        PortfolioGroupConfig original = config();
        hzMap.put(PORTFOLIO_ID, original);

        // update
        PortfolioGroupConfig updated = config().toBuilder()
            .setExecutionConfig(ExecutionConfig.newBuilder()
                .setPercentageFee("0.99999")
                .build())
            .clearHedgingConfig()
            .build();
        hzMap.put(PORTFOLIO_ID, updated);

        // get and assert
        PortfolioGroupConfig got = hzMap.get(PORTFOLIO_ID);
        assertThat(got).isEqualTo(updated);

        // evict, get and assert
        hzMap.evictAll();
        PortfolioGroupConfig got2 = hzMap.get(PORTFOLIO_ID);
        assertThat(got2).isEqualTo(updated);

        // assert DB size
        DB.awaitExpectedRecordsInTable(TABLE_NAME, 1);
        DB.logTableContent(TABLE_NAME);
    }

    private PortfolioGroupConfig config() {
        return config(PORTFOLIO_ID);
    }

    private PortfolioGroupConfig config(String portfolioId) {
        return PortfolioGroupConfig.newBuilder()
            .setId(portfolioId)
            .setName(portfolioId)
            .setExecutionConfig(ExecutionConfig.newBuilder()
                .setCounterPortfolio("Bank")
                .setAgencyTradingAccount("Kraken")
                .setChargeExchangeFee(TernaryBool.TRUE)
                .setFixedFee("1000")
                .setFixedFeeCurrency("JPY")
                .setMinFee("3")
                .setMinFeeCurrency("JPY")
                .setPercentageFee("0.0006")
                .setPercentageFeeCurrency("USD")
                .build())
            .setHedgingConfig(HedgingConfig.newBuilder()
                .setAutoHedging(TernaryBool.TRUE)
                .setTargetAccountId("Binance")
                .addThreshold(ThresholdConfig.newBuilder()
                    .setHighThreshold("10")
                    .setLowThreshold("-10")
                    .setTargetExposure("0")
                    .build())
                .build())
            .setPricingConfig(PricingConfig.newBuilder()
                .addPricingSource(InstrumentKey.newBuilder()
                    .setVenueAccount("Coinbase")
                    .setInstrumentId("BTCUSD")
                    .build())
                .setMarkup("0.12345")
                .setClientSideVenueName("Bank")
                .build())
            .addInstrument(InstrumentConfig.newBuilder()
                .setInstrumentId("ETHUSD")
                .setPricingConfig(PricingConfig.newBuilder()
                    .addPricingSource(InstrumentKey.newBuilder()
                        .setVenueAccount("Coinbase")
                        .setInstrumentId("ETHUSD")
                        .build())
                    .setMarkup("0.00045")
                    .setClientSideVenueName("Bank")
                    .build())
                .build())
            .build();
    }
}

@DirtiesContext(classMode = BEFORE_CLASS)
@ExtendWith(PostgreSQLSetupExtension.class)
class PostgreSqlBrokerDeskPortfolioGroupConfigPersistenceTest extends BrokerDeskPortfolioGroupConfigPersistenceTest {
}

@DirtiesContext(classMode = BEFORE_CLASS)
@ExtendWith(OracleSetupExtension.class)
@Disabled
class OracleBrokerDeskPortfolioGroupConfigPersistenceTest extends BrokerDeskPortfolioGroupConfigPersistenceTest {
}
