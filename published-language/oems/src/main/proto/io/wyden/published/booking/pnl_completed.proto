syntax = "proto3";

option java_multiple_files = true;

package io.wyden.published.booking;

import "io/wyden/published/booking/ledger_entry_snapshot.proto";
import "io/wyden/published/booking/position_snapshot.proto";
import "io/wyden/published/booking/reservation_snapshot.proto";
import "io/wyden/published/booking/transaction_snapshot.proto";
import "io/wyden/published/common/metadata.proto";
import "io/wyden/published/oems/enums.proto";

message PnlCompleted {
  io.wyden.published.common.Metadata metadata = 1;

  int64 sequence_number = 2;

  TransactionSnapshot transaction_snapshot = 3;

  ReservationSnapshot reservation_snapshot = 4;

  repeated LedgerEntrySnapshot ledger_entry_snapshot = 5;

  repeated PositionSnapshot position_snapshot = 6;
}
