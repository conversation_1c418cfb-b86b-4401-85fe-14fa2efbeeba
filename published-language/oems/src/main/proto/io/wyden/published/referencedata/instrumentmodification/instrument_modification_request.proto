syntax = "proto3";

option java_multiple_files = true;

package io.wyden.published.referencedata.instrumentmodification;

import "io/wyden/published/referencedata/instrumentmodification/instrument_modification_command_type.proto";
import "io/wyden/published/referencedata/instrumentmodification/instrument_to_modify.proto";
import "io/wyden/published/common/metadata.proto";

message InstrumentModificationRequest {
  string message_id = 1;
  InstrumentModificationCommandType instrument_modification_command_type = 2;
  InstrumentToModify instrument_to_modify = 3;
  string client_id = 4;
  io.wyden.published.common.Metadata metadata = 5;
}