package io.wyden.apiserver.rest.marketdata;

import io.micrometer.common.util.StringUtils;
import io.wyden.accessgateway.client.apikey.dto.AuthResponseDto;
import io.wyden.apiserver.rest.marketdata.model.L1Event;
import io.wyden.apiserver.rest.marketdata.model.L2Event;
import io.wyden.apiserver.rest.marketdata.model.MdClientRequest;
import io.wyden.apiserver.rest.security.AccessService;
import io.wyden.apiserver.rest.security.SecurityService;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.UUID;
import javax.annotation.Nullable;

import static com.google.common.base.Strings.isNullOrEmpty;
import static io.wyden.accessgateway.client.permission.Permission.PORTFOLIO_TRADE;
import static io.wyden.accessgateway.client.permission.Permission.VENUE_ACCOUNT_TRADE;
import static io.wyden.apiserver.rest.security.AccessService.User.user;

@Service
public class SecuredMarketDataService {

    private final AccessService accessService;
    private final SecurityService securityService;
    private final MarketDataService marketDataService;

    public SecuredMarketDataService(AccessService accessService, SecurityService securityService, MarketDataService marketDataService) {
        this.accessService = accessService;
        this.securityService = securityService;
        this.marketDataService = marketDataService;
    }

    public Flux<L1Event> subscribeL1(@Nullable String venueAccount, String instrumentId, @Nullable String portfolioId, String apiKeyId, String apiKeySecret) {
        verifyAccess(apiKeyId, apiKeySecret, portfolioId, venueAccount);
        return marketDataService.subscribeL1(venueAccount, instrumentId, portfolioId, new MdClientRequest(apiKeyId, UUID.randomUUID().toString()));
    }

    public Flux<L2Event> subscribeL2(@Nullable String venueAccount, String instrumentId, @Nullable String portfolioId, String apiKeyId, String apiKeySecret) {
        verifyAccess(apiKeyId, apiKeySecret, portfolioId, venueAccount);
        return marketDataService.subscribeL2(venueAccount, instrumentId, portfolioId, new MdClientRequest(apiKeyId, UUID.randomUUID().toString()));
    }

    private void verifyAccess(String apiKey, String apiSecret, @Nullable String portfolioId, @Nullable String venueAccountId) {
        if (isNullOrEmpty(apiKey) || isNullOrEmpty(apiSecret)) {
            throw new AccessDeniedException("Unauthenticated access is not permitted in MarketData API");
        }
        if (StringUtils.isBlank(portfolioId) && StringUtils.isBlank(venueAccountId)) {
            throw new AccessDeniedException("Either portfolio or venueAccount is required");
        }

        AuthResponseDto authResponseDto = securityService.authenticate(apiKey, apiSecret);
        if (StringUtils.isNotBlank(portfolioId) && !accessService.hasPermission(user(authResponseDto), PORTFOLIO_TRADE, portfolioId)) {
            throw new AccessDeniedException("Access Denied");
        }
        if (StringUtils.isNotBlank(venueAccountId) && !accessService.hasPermission(user(authResponseDto), VENUE_ACCOUNT_TRADE, venueAccountId)) {
            throw new AccessDeniedException("Access Denied");
        }
    }
}
