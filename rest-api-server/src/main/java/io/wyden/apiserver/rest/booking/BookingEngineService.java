package io.wyden.apiserver.rest.booking;

import io.wyden.apiserver.rest.booking.BookingModel.LedgerEntryResponse;
import io.wyden.apiserver.rest.booking.BookingModel.PositionResponse;
import io.wyden.apiserver.rest.booking.BookingModel.TransactionResponse;
import io.wyden.apiserver.rest.infrastructure.rabbit.BookingEngineCommandEmitter;
import io.wyden.apiserver.rest.infrastructure.rabbit.BookingEngineCommandResultConsumer;
import io.wyden.apiserver.rest.referencedata.portfolio.service.SecuredPortfolioService;
import io.wyden.apiserver.rest.security.model.Scope;
import io.wyden.apiserver.rest.security.model.ScopeExtractor;
import io.wyden.apiserver.rest.security.model.WydenAuthenticationToken;
import io.wyden.cloud.utils.rest.pagination.PaginationModel.CursorConnection;
import io.wyden.cloud.utils.rest.pagination.PaginationProtoToModelMapper;
import io.wyden.published.booking.LedgerEntrySearch;
import io.wyden.published.booking.PositionSearch;
import io.wyden.published.booking.TransactionRequest;
import io.wyden.published.booking.TransactionSearch;
import io.wyden.published.booking.TransactionSnapshot;
import io.wyden.published.booking.command.Command;
import io.wyden.published.common.Metadata;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.List;
import java.util.UUID;
import java.util.logging.Level;
import java.util.stream.Collectors;

@Service
public class BookingEngineService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BookingEngineService.class);

    private final BookingEngineHttpClient bookingEngineHttpClient;
    private final BookingMessageToModelMapper bookingMapper;
    private final TransactionFromProtoMapper transactionFromProtoMapper;
    private final TransactionToProtoMapper transactionToProtoMapper;
    private final PositionChangedEventConsumer positionChangedEventConsumer;
    private final TransactionCreatedEventConsumer transactionCreatedEventConsumer;
    private final BookingEngineCommandEmitter commandEmitter;
    private final BookingEngineCommandResultConsumer responseObserver;
    private final SecuredPortfolioService securedPortfolioService;
    private final Duration rabbitTimeout;

    public BookingEngineService(BookingEngineHttpClient bookingEngineHttpClient,
                                BookingMessageToModelMapper bookingMapper,
                                TransactionFromProtoMapper transactionFromProtoMapper,
                                TransactionToProtoMapper transactionToProtoMapper,
                                PositionChangedEventConsumer positionChangedEventConsumer,
                                TransactionCreatedEventConsumer transactionCreatedEventConsumer,
                                BookingEngineCommandEmitter commandEmitter,
                                BookingEngineCommandResultConsumer responseObserver,
                                SecuredPortfolioService securedPortfolioService,
                                @Value("${booking.command.response.timeoutInSeconds}") int timeoutInSeconds
    ) {
        this.bookingEngineHttpClient = bookingEngineHttpClient;
        this.bookingMapper = bookingMapper;
        this.transactionFromProtoMapper = transactionFromProtoMapper;
        this.transactionToProtoMapper = transactionToProtoMapper;
        this.positionChangedEventConsumer = positionChangedEventConsumer;
        this.transactionCreatedEventConsumer = transactionCreatedEventConsumer;
        this.commandEmitter = commandEmitter;
        this.responseObserver = responseObserver;
        this.securedPortfolioService = securedPortfolioService;
        this.rabbitTimeout = Duration.ofSeconds(timeoutInSeconds);
    }

    public Mono<CursorConnection<PositionResponse>> getPositions(BookingModel.PositionSearchInput searchInput, WydenAuthenticationToken token) {
        PositionSearch search = BookingModel.PositionBookingSearch.wrapProto(searchInput, token.getClientId());

        LOGGER.debug("Looking up positions for search: ({})", search);

        return bookingEngineHttpClient.getPositionsProto(search)
                .log("booking", Level.FINE)
            .map(positionConnection -> getPositionConnection(positionConnection, portfolioId -> getScopes(token, portfolioId), portfolioId -> getDynamicScopes(token, portfolioId)));
    }

    public Mono<CursorConnection<LedgerEntryResponse>> getLedgerEntries(BookingModel.LedgerEntrySearchInput searchInput, WydenAuthenticationToken token) {
        LedgerEntrySearch search = BookingModel.LedgerEntryBookingSearch.wrapProto(searchInput, token.getClientId());

        LOGGER.debug("Looking up ledger entries for search: ({})", search);

        return bookingEngineHttpClient.getLedgerEntries(search)
                .log("booking", Level.FINE)
                .map(this::getLedgerEntryConnection);
    }

    public Mono<CursorConnection<TransactionResponse>> getTransactions(BookingModel.TransactionSearchInput searchInput, WydenAuthenticationToken token) {
        TransactionSearch transactionSearch = BookingModel.TransactionBookingSearch.wrapToProto(searchInput, token.getClientId());

        LOGGER.debug("Looking up transactions for search: ({})", searchInput);

        return bookingEngineHttpClient.getTransactionsProto(transactionSearch)
                .log("booking", Level.FINE)
                .map(this::getTransactionConnection);
    }

    public Mono<List<String>> getTransactionTypes() {
        LOGGER.debug("Looking up all transaction types");
        return bookingEngineHttpClient.getTransactionTypes()
                .log("booking", Level.FINE);
    }

    public Mono<List<String>> getPositionTypes() {
        LOGGER.debug("Looking up all positions types");
        return bookingEngineHttpClient.getPositionTypes()
                .log("booking", Level.FINE);
    }

    public Mono<String> getSystemCurrency() {
        LOGGER.debug("Looking up system currency");
        return bookingEngineHttpClient.getSystemCurrency()
                .log("booking", Level.FINE);
    }

    private CursorConnection<PositionResponse> getPositionConnection(io.wyden.published.common.CursorConnection positionConnection, ScopeExtractor scopeExtractor, ScopeExtractor dynamicScopeExtractor) {
        return PaginationProtoToModelMapper.map(positionConnection, node -> bookingMapper.getPositionResponse(node.getPosition(), scopeExtractor, dynamicScopeExtractor));
    }

    private CursorConnection<LedgerEntryResponse> getLedgerEntryConnection(io.wyden.published.common.CursorConnection ledgerEntryConnection) {
        return PaginationProtoToModelMapper.map(ledgerEntryConnection, node -> bookingMapper.getLedgerEntryResponse(node.getLedgerEntry()));
    }

    private CursorConnection<TransactionResponse> getTransactionConnection(io.wyden.published.common.CursorConnection transactionConnection) {
        return PaginationProtoToModelMapper.map(transactionConnection, node -> transactionFromProtoMapper.getTransactionResponse(node.getTransaction()));
    }

    public Mono<TransactionSnapshot> addTransaction(BookingModel.TransactionInput request) {
        LOGGER.debug("Adding transaction: {}", request);
        TransactionInputValidator.validate(request);
        TransactionRequest transactionRequest = transactionToProtoMapper.map(request);

        return addTransactionViaRabbit(transactionRequest);
    }

    private Mono<TransactionSnapshot> addTransactionViaRabbit(TransactionRequest transactionRequest) {
        String requestId = UUID.randomUUID().toString();
        commandEmitter.emitCommand(Command.newBuilder()
            .setMetadata(Metadata.newBuilder().setRequestId(requestId).build())
            .setTransactionRequest(transactionRequest)
            .build());
        return responseObserver.getFlux()
            .filter(res -> res.getMetadata().getInResponseToRequestId().equals(requestId))
            .timeout(rabbitTimeout)
            .flatMap(res -> {
                if (res.hasSuccess() && res.getSuccess().hasTransaction()) {
                    return Mono.just(res);
                } else {
                    String msg = String.format("Failed to add transaction in BookingEngine: %s", res);
                    LOGGER.error(msg);
                    return Mono.error(new RuntimeException(msg));
                }
            })
            .map(res -> res.getSuccess().getTransaction())
            .next();
    }

    private Mono<TransactionSnapshot> addTransactionViaHttp(TransactionRequest transactionRequest) {
        return bookingEngineHttpClient.createTransaction(transactionRequest)
            .flatMap(result -> {
                if (result.hasError()) {
                    String message = result.getError().getMessage();
                    return Mono.error(new IllegalArgumentException(message));
                }

                return Mono.just(result.getSuccess().getTransaction());
            });
    }

    public Flux<PositionResponse> subscribePositionStream(BookingModel.PositionSearchInput searchInput, WydenAuthenticationToken token) {
        LOGGER.debug("Looking up & subscribing positions for search: ({})", searchInput);

        Flux<PositionResponse> snapshot = getPositions(searchInput, token)
            .flatMapIterable(CursorConnection::getAllNodes)
                .log("Position stream (snapshot)", Level.FINE);

        Flux<PositionResponse> updates = getPositionStream(searchInput, token, portfolioId -> getScopes(token, portfolioId), portfolioId -> getDynamicScopes(token, portfolioId))
                .log("Position stream (changes)", Level.FINE);

        return snapshot.concatWith(updates);
    }

    private Flux<PositionResponse> getPositionStream(BookingModel.PositionSearchInput searchInput, WydenAuthenticationToken token, ScopeExtractor scopeExtractor, ScopeExtractor dynamicScopeExtractor) {
        return positionChangedEventConsumer.stream(searchInput)
            .mapNotNull(position -> bookingMapper.getPositionResponse(position, scopeExtractor, dynamicScopeExtractor));
    }

    public Flux<TransactionResponse> subscribeTransactionStream(BookingModel.TransactionSearchInput searchInput, WydenAuthenticationToken token) {
        LOGGER.debug("Looking up & subscribing transactions for search: ({})", searchInput);

        Flux<TransactionResponse> snapshot = getTransactions(searchInput, token)
            .flatMapIterable(CursorConnection::getAllNodes)
                .log("Transaction stream (snapshot)", Level.FINE);

        Flux<TransactionResponse> updates = getTransactionStream(searchInput)
                .log("Transaction stream (changes)", Level.FINE);

        return snapshot.concatWith(updates);
    }

    private Flux<TransactionResponse> getTransactionStream(BookingModel.TransactionSearchInput searchInput) {
        return transactionCreatedEventConsumer.stream(searchInput)
            .mapNotNull(transactionFromProtoMapper::getTransactionResponse);
    }

    private List<Scope> getScopes(WydenAuthenticationToken token, String portfolioId) {
        return securedPortfolioService.getScopes(token, portfolioId).stream()
            .map(Scope::parse)
            .collect(Collectors.toList());
    }

    private List<Scope> getDynamicScopes(WydenAuthenticationToken token, String portfolioId) {
        return securedPortfolioService.getDynamicScopes(token, portfolioId).stream()
            .map(Scope::parse)
            .collect(Collectors.toList());
    }
}
