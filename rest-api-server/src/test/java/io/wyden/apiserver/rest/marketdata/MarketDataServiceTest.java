package io.wyden.apiserver.rest.marketdata;

import io.micrometer.core.instrument.MeterRegistry;
import io.wyden.apiserver.rest.infrastructure.rabbit.EventLogConsumer;
import io.wyden.apiserver.rest.marketdata.emitter.EventLogEmitter;
import io.wyden.apiserver.rest.marketdata.model.L1Event;
import io.wyden.apiserver.rest.marketdata.model.OrderBookEvent;
import io.wyden.apiserver.rest.marketdata.model.L2Event;
import io.wyden.apiserver.rest.marketdata.model.MarketDataDepth;
import io.wyden.apiserver.rest.marketdata.model.MarketDataIdentifierDTO;
import io.wyden.apiserver.rest.marketdata.model.MarketDataSubscriptionKey;
import io.wyden.apiserver.rest.marketdata.model.MarketDataTypeDTO;
import io.wyden.apiserver.rest.marketdata.model.MdClientRequest;
import io.wyden.apiserver.rest.marketdata.model.OrderBookLevelDTO;
import io.wyden.apiserver.rest.referencedata.instruments.service.InstrumentsRepository;
import io.wyden.cloud.utils.test.TracingMock;
import io.wyden.cloudutils.telemetry.Telemetry;
import io.wyden.published.client.ClientSide;
import io.wyden.published.common.Metadata;
import io.wyden.published.marketdata.InstrumentKey;
import io.wyden.published.marketdata.MarketDataRequest;
import io.wyden.published.referencedata.BaseInstrument;
import io.wyden.published.referencedata.Instrument;
import io.wyden.published.referencedata.InstrumentIdentifiers;
import io.wyden.published.referencedata.VenueAccount;
import io.wyden.published.referencedata.VenueType;
import io.wyden.referencedata.client.PortfoliosCacheFacade;
import io.wyden.referencedata.client.VenueAccountCacheFacade;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;
import reactor.test.StepVerifier;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.ZonedDateTime;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class MarketDataServiceTest {

    private static final String INSTRUMENT_ID = "BTCUSD@FOREX@TestVenue";
    private static final String nullPortfolio = null;
    private static final String ADAPTER_TICKER = "BTCUSD";
    private static final String TEST_VENUE = "TestVenue";
    private static final String TEST_VENUE_ACCOUNT = "TestVenueAccount";
    private static final String REQUESTER = "userId";

    MarketDataService marketDataService;
    DirectMarketDataSubscriptionService direct;
    PreconfiguredMarketDataSubscriptionService preconfiguired;
    @Mock
    MarketDataAdapterService marketDataAdapterService;
    @Mock
    InstrumentsRepository instrumentsRepository;
    @Mock
    EventLogEmitter eventLogEmitter;
    @Mock
    EventLogConsumer eventLogConsumer;
    @Mock
    PortfoliosCacheFacade portfolioCache;
    @Mock
    VenueAccountCacheFacade venueAccountCacheFacade;
    @Mock
    TickService tickService;

    Sinks.Many<L1Event> l1Sink = Sinks.many().multicast().directBestEffort();
    Sinks.Many<L2Event> l2Sink = Sinks.many().multicast().directBestEffort();

    @BeforeEach
    void setup() {
        Telemetry telemetryMock = new Telemetry(TracingMock.createMock(), mock(MeterRegistry.class));
        LastMarketDataEventCache lastMarketDataEventCache = new LastMarketDataEventCache();
        when(marketDataAdapterService.fetchMarketDataL1(any())).thenReturn(l1Sink.asFlux().log());
        when(marketDataAdapterService.fetchMarketDataL2(any())).thenReturn(l2Sink.asFlux().log());
        when(marketDataAdapterService.requestDirectStream(any(), anyString()))
            .thenAnswer(invocation -> {
                MarketDataSubscriptionKey subscriptionKey = invocation.getArgument(0);
                String subscriptionId = invocation.getArgument(1);
                return createSubscriptionRequest(subscriptionKey.venueAccount(), subscriptionKey.instrumentId(), subscriptionId, subscriptionKey.depth());
            });

        when(instrumentsRepository.find(INSTRUMENT_ID)).thenReturn(getInstrument());
        when(eventLogConsumer.streamResults()).thenReturn(Flux.never());
        when(instrumentsRepository.find(anyString())).thenReturn(Instrument.newBuilder().setBaseInstrument(BaseInstrument.newBuilder().setVenueName(TEST_VENUE).build()).build());
        when(venueAccountCacheFacade.find(anyString())).thenReturn(Optional.of(VenueAccount.newBuilder().setVenueName(TEST_VENUE).setId(TEST_VENUE_ACCOUNT).setVenueAccountName(TEST_VENUE_ACCOUNT).build()));

        //
        direct = spy(new DirectMarketDataSubscriptionService(marketDataAdapterService, instrumentsRepository, telemetryMock, eventLogConsumer, eventLogEmitter, lastMarketDataEventCache, venueAccountCacheFacade, 15));
        preconfiguired = spy(new PreconfiguredMarketDataSubscriptionService(marketDataAdapterService, telemetryMock,
            15, lastMarketDataEventCache, eventLogConsumer, eventLogEmitter, portfolioCache));
        SubscriptionServiceProvider subscriptionServiceProvider = new SubscriptionServiceProvider(direct, preconfiguired);
        marketDataService = new MarketDataService(subscriptionServiceProvider, tickService, eventLogEmitter);
    }

    @Test
    void shouldSubscribeAndUnsubscribeL1() {
        // given
        MarketDataIdentifierDTO marketDataIdentifier = new MarketDataIdentifierDTO(INSTRUMENT_ID, TEST_VENUE, TEST_VENUE_ACCOUNT, ZonedDateTime.now(), "");

        L1Event l1Event1 = new L1Event(
            marketDataIdentifier,
            BigDecimal.valueOf(105.0),
            BigDecimal.valueOf(1.0),
            BigDecimal.valueOf(103.0),
            BigDecimal.valueOf(1.0),
            null,
            null,
            null,
            ClientSide.SIDE_UNDETERMINED,
            MarketDataTypeDTO.BIDASK);

        L1Event l1Event2 = new L1Event(
            marketDataIdentifier,
            null,
            null,
            null,
            null,
            BigDecimal.valueOf(1.0),
            BigDecimal.valueOf(104.0),
            BigDecimal.valueOf(1.0),
            ClientSide.SIDE_UNDETERMINED,
            MarketDataTypeDTO.TRADE);

        MdClientRequest mdClientRequest = new MdClientRequest(REQUESTER, UUID.randomUUID().toString());

        // when
        Flux<L1Event> l1Flux = marketDataService.subscribeL1(TEST_VENUE_ACCOUNT, INSTRUMENT_ID, nullPortfolio, mdClientRequest);

        // then
        StepVerifier
            .create(l1Flux)
            .then(() -> l1Sink.tryEmitNext(l1Event1))
            .then(() -> l1Sink.tryEmitNext(l1Event2))
            .expectNext(l1Event1)
            .expectNext(l1Event2)
            .thenCancel()
            .verify(Duration.ofSeconds(5));

        verify(direct, times(1)).unsubscribe(new MarketDataSubscriptionKey(INSTRUMENT_ID, TEST_VENUE_ACCOUNT, MarketDataDepth.L1, nullPortfolio), mdClientRequest);
        verify(direct, times(0)).unsubscribe(new MarketDataSubscriptionKey(INSTRUMENT_ID, TEST_VENUE_ACCOUNT, MarketDataDepth.L2, nullPortfolio), mdClientRequest);
    }

    @Test
    void shouldSubscribeAndUnsubscribeL2() {
        // given
        MarketDataIdentifierDTO marketDataIdentifier = new MarketDataIdentifierDTO(INSTRUMENT_ID, TEST_VENUE, TEST_VENUE_ACCOUNT, ZonedDateTime.now(), "");
        OrderBookLevelDTO bid1 = new OrderBookLevelDTO(BigDecimal.valueOf(10.0), BigDecimal.valueOf(100.0), null);
        OrderBookLevelDTO bid2 = new OrderBookLevelDTO(BigDecimal.valueOf(20.0), BigDecimal.valueOf(200.0), null);
        OrderBookLevelDTO ask1 = new OrderBookLevelDTO(BigDecimal.valueOf(100.0), BigDecimal.valueOf(10.0), null);
        OrderBookLevelDTO ask2 = new OrderBookLevelDTO(BigDecimal.valueOf(200.0), BigDecimal.valueOf(20.0), null);

        OrderBookEvent orderBookEvent = new OrderBookEvent(marketDataIdentifier, Map.of("10.0", bid1, "20.0", bid2), Map.of("100.0", ask1, "200.0", ask2), bid1, ask1);

        MdClientRequest mdClientRequest = new MdClientRequest(REQUESTER, UUID.randomUUID().toString());
        // when
        Flux<L2Event> l2Flux = marketDataService.subscribeL2(TEST_VENUE_ACCOUNT, INSTRUMENT_ID, nullPortfolio, mdClientRequest);
        // then
        StepVerifier
            .create(l2Flux)
            .then(() -> l2Sink.tryEmitNext(orderBookEvent))
            .expectNext(orderBookEvent)
            .thenCancel()
            .verify(Duration.ofSeconds(5));

        verify(direct, times(0)).unsubscribe(new MarketDataSubscriptionKey(INSTRUMENT_ID, TEST_VENUE_ACCOUNT, MarketDataDepth.L1, nullPortfolio), mdClientRequest);
        verify(direct, times(1)).unsubscribe(new MarketDataSubscriptionKey(INSTRUMENT_ID, TEST_VENUE_ACCOUNT, MarketDataDepth.L2, nullPortfolio), mdClientRequest);
    }

    @Test
    void callUnsubscribeForEveryFluxButIgnoreEventsOnLast() {
        // given
        MdClientRequest mdClientRequest1 = new MdClientRequest(REQUESTER, UUID.randomUUID().toString());
        MdClientRequest mdClientRequest2 = new MdClientRequest("userId2", UUID.randomUUID().toString());
        MdClientRequest mdClientRequest3 = new MdClientRequest("userId3", UUID.randomUUID().toString());
        Disposable l1Flux1 = marketDataService.subscribeL1(TEST_VENUE_ACCOUNT, INSTRUMENT_ID, nullPortfolio, mdClientRequest1).subscribe();
        Disposable l1Flux2 = marketDataService.subscribeL1(TEST_VENUE_ACCOUNT, INSTRUMENT_ID, nullPortfolio, mdClientRequest2).subscribe();
        Disposable l1Flux3 = marketDataService.subscribeL1(TEST_VENUE_ACCOUNT, INSTRUMENT_ID, nullPortfolio, mdClientRequest3).subscribe();
        // when
        l1Flux1.dispose();
        // then
        verify(direct, times(1)).unsubscribe(eq(new MarketDataSubscriptionKey(INSTRUMENT_ID, TEST_VENUE_ACCOUNT, MarketDataDepth.L1, nullPortfolio)), any());
        // when
        l1Flux2.dispose();
        // then
        verify(direct, times(2)).unsubscribe(eq(new MarketDataSubscriptionKey(INSTRUMENT_ID, TEST_VENUE_ACCOUNT, MarketDataDepth.L1, nullPortfolio)), any());
        // when
        l1Flux3.dispose();
        // then
        verify(direct, times(3)).unsubscribe(eq(new MarketDataSubscriptionKey(INSTRUMENT_ID, TEST_VENUE_ACCOUNT, MarketDataDepth.L1, nullPortfolio)), any());
    }

    @Test
    void unsubscribeL1AndL2Separately() {
        // given
        MdClientRequest mdClientRequest1 = new MdClientRequest(REQUESTER, UUID.randomUUID().toString());
        MdClientRequest mdClientRequest2 = new MdClientRequest(REQUESTER, UUID.randomUUID().toString());
        Disposable l1Flux = marketDataService.subscribeL1(TEST_VENUE_ACCOUNT, INSTRUMENT_ID, nullPortfolio, mdClientRequest1).subscribe();
        Disposable l2Flux = marketDataService.subscribeL2(TEST_VENUE_ACCOUNT, INSTRUMENT_ID, nullPortfolio, mdClientRequest2).subscribe();
        // when
        l1Flux.dispose();
        // then
        verify(direct, times(1)).unsubscribe(new MarketDataSubscriptionKey(INSTRUMENT_ID, TEST_VENUE_ACCOUNT, MarketDataDepth.L1, nullPortfolio), mdClientRequest1);
        verify(direct, times(0)).unsubscribe(new MarketDataSubscriptionKey(INSTRUMENT_ID, TEST_VENUE_ACCOUNT, MarketDataDepth.L2, nullPortfolio), mdClientRequest2);
        // when
        l2Flux.dispose();
        // then
        verify(direct, times(1)).unsubscribe(new MarketDataSubscriptionKey(INSTRUMENT_ID, TEST_VENUE_ACCOUNT, MarketDataDepth.L1, nullPortfolio), mdClientRequest1);
        verify(direct, times(1)).unsubscribe(new MarketDataSubscriptionKey(INSTRUMENT_ID, TEST_VENUE_ACCOUNT, MarketDataDepth.L2, nullPortfolio), mdClientRequest2);
    }

    private Instrument getInstrument() {
        return Instrument.newBuilder()
            .setBaseInstrument(BaseInstrument.newBuilder()
                .setVenueType(VenueType.STREET)
                .setVenueName(TEST_VENUE).build())
            .setInstrumentIdentifiers(InstrumentIdentifiers.newBuilder().setInstrumentId(INSTRUMENT_ID).setAdapterTicker(ADAPTER_TICKER).build())
            .build();
    }

    private MarketDataRequest createSubscriptionRequest(String venueAccount, String instrumentId, String subscriptionId, MarketDataDepth marketDataDepth) {
        Instrument instrument = instrumentsRepository.find(instrumentId);
        return MarketDataRequest.newBuilder()
            .setMarketDepth(marketDataDepth.getDepth())
            .setInstrumentKey(InstrumentKey.newBuilder()
                .setInstrumentId(instrumentId)
                .setVenueAccount(venueAccount)
                .build())
            .setTicker(instrument.getInstrumentIdentifiers().getAdapterTicker())
            .setMetadata(Metadata.newBuilder()
                .setRequesterId("TEST")
                .setRequestId(subscriptionId)
                .build())
            .setRefreshId(UUID.randomUUID().toString())
            .build();
    }
}
