package io.wyden.apiserver.rest.trading.model;

import com.google.common.base.MoreObjects;
import org.apache.commons.lang3.StringUtils;

public record OrderCancelRequest(String orderId,
                                 String origClientId,
                                 String origClOrderId,
                                 String clOrderId,
                                 boolean forceCancel) {

    @Override
    public String orderId() {
        return StringUtils.defaultString(orderId);
    }

    @Override
    public String origClientId() {
        return StringUtils.defaultString(origClientId);
    }

    @Override
    public String origClOrderId() {
        return StringUtils.defaultString(origClOrderId);
    }

    @Override
    public String clOrderId() {
        return StringUtils.defaultString(clOrderId);
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("orderId", orderId)
            .add("origClientId", origClientId)
            .add("origClOrderId", origClOrderId)
            .add("clOrderId", clOrderId)
            .add("forceCancel", forceCancel)
            .toString();
    }
}
