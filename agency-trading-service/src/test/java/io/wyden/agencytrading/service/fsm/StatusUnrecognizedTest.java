package io.wyden.agencytrading.service.fsm;

import io.wyden.agencytrading.model.AgencyTradingOrderState;
import io.wyden.agencytrading.service.tracking.FailureRequeueException;
import io.wyden.published.common.Metadata;
import io.wyden.published.oems.OemsExecType;
import io.wyden.published.oems.OemsOrderStatus;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static io.wyden.published.oems.OemsOrderStatus.ORDER_STATUS_UNSPECIFIED;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_CANCELED;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_NEW;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_PENDING_CANCEL;
import static io.wyden.published.oems.OemsOrderStatus.STATUS_PENDING_NEW;
import static io.wyden.published.oems.OemsResponse.Result.OEMS_ORDER_REGISTRATION_ERROR_DUPLICATE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.verify;
class StatusUnrecognizedTest extends OrderServiceBase {

    private static final String ZERO = "0";

    OrderState orderState;
    String requestId;

    @BeforeEach
    void beforeEach() {
        requestId = UUID.randomUUID().toString();
        AgencyTradingOrderState smartOrderState = AgencyTradingOrderState.newBuilder()
            .setRequest(order)
            .setClientId(order.getClientId())
            .setQuantity(order.getQuantity())
            .setFilledQuantity(ZERO)
            .setRemainingQuantity(order.getQuantity())
            .setAvgPrice(ZERO)
            .setAvgPriceWithMarkup(ZERO)
            .setClosed(false)
            .addCurrentStatus(STATUS_PENDING_NEW)
            .addCurrentStatus(STATUS_NEW)
            .addCurrentStatus(STATUS_CANCELED)
            .addCurrentStatus(ORDER_STATUS_UNSPECIFIED)
            .addCurrentStatus(STATUS_PENDING_CANCEL)
            .setPendingCancelRequestId(requestId)
            .setTotalFeeCharged(ZERO)
            .setTotalPercFee(ZERO)
            .build();
        orderCache.add(smartOrderState);
        orderState = new OrderState(smartOrderState);
    }

    @Test
    void whenNewOrderThenRejectWithDuplicateOrderId() {
        OrderStatus orderStatus = new StatusUnrecognized(OemsOrderStatus.STATUS_CANCELED_VALUE);
        OrderContext context = new OrderContext(orderService);
        context.setOrderState(orderState);
        orderStatus.onNewOrder(context, order);
        context.updateAgencyTradingOrderState();
        verify(oemsResponseEmitter, ONCE).emit(oemsResponseCaptor.capture());

        OemsResponse oemsResponse = oemsResponseCaptor.getValue();
        assertThat(oemsResponse.getResponseType()).isEqualTo(OemsResponse.OemsResponseType.EXECUTION_REPORT);
        assertThat(oemsResponse.getExecType()).isEqualTo(OemsExecType.REJECTED);
        assertThat(oemsResponse.getOrderId()).isEqualTo(order.getOrderId());
        assertThat(oemsResponse.getOrderStatus()).isEqualTo(ORDER_STATUS_UNSPECIFIED);
        assertThat(oemsResponse.getRequestResult()).isEqualTo(OEMS_ORDER_REGISTRATION_ERROR_DUPLICATE);
        assertThat(oemsResponse.getReason()).isEqualTo("Duplicate orderId");
    }

    @Test
    void whenCancelRequestThenRequeue() {
        OemsRequest oemsCancel = defaultCancel();
        Exception ex = assertThrows(FailureRequeueException.class, () -> orderService.onOemsRequest(oemsCancel));
        assertThat(ex.getMessage()).isEqualTo("Received Cancel when in unrecognized state value=0");
    }

    @Test
    @Disabled //TODO tkow - to enable when CancelReject is implemented
    void whenCancelRejectThenRequeue() {
        OemsResponse venueCancelReject = defaultCancelReject("requestId");
        Exception ex = assertThrows(FailureRequeueException.class, () -> orderService.onCancelReject(venueCancelReject));
        assertThat(ex.getMessage()).isEqualTo("Received CancelReject when in unrecognized state value=0");
    }

    @Test
    void whenPendingNewThenRequeue() {
        OemsResponse executionReport = pendingNewExecutionReport();
        Exception ex = assertThrows(FailureRequeueException.class, () -> orderService.onExecutionReport(executionReport));
        assertThat(ex.getMessage()).isEqualTo("Received PendingNew when in unrecognized state value=0");
    }

    @Test
    void whenNewThenRequeue() {
        OemsResponse executionReport = newExecutionReport();
        Exception ex = assertThrows(FailureRequeueException.class, () -> orderService.onExecutionReport(executionReport));
        assertThat(ex.getMessage()).isEqualTo("Received New when in unrecognized state value=0");
    }

    @Test
    void whenRejectedThenRequeue() {
        OemsResponse executionReport = rejectedExecutionReport("no reason");
        Exception ex = assertThrows(FailureRequeueException.class, () -> orderService.onExecutionReport(executionReport));
        assertThat(ex.getMessage()).isEqualTo("Received Rejected when in unrecognized state value=0");
    }

    @Test
    void whenPartialFillThenRequeue() {
        OemsResponse executionReport = partialFillExecutionReport("3.0", "3.0", "30", "30");
        Exception ex = assertThrows(FailureRequeueException.class, () -> orderService.onExecutionReport(executionReport));
        assertThat(ex.getMessage()).isEqualTo("Received Trade when in unrecognized state value=0");
    }

    @Test
    void whenFilledThenRequeue() {
        OemsResponse executionReport = filledExecutionReport("10.0", "10.0", "30", "30");
        Exception ex = assertThrows(FailureRequeueException.class, () -> orderService.onExecutionReport(executionReport));
        assertThat(ex.getMessage()).isEqualTo("Received Trade when in unrecognized state value=0");
    }

    @Test
    void whenCancelledThenRequeue() {
        OemsResponse executionReport = cancelledExecutionReport("10.0", "30");
        Exception ex = assertThrows(FailureRequeueException.class, () -> orderService.onExecutionReport(executionReport));
        assertThat(ex.getMessage()).isEqualTo("Received Canceled when in unrecognized state value=0");
    }

    @Test
    void whenUnrecognizedThenRequeue() {
        OemsResponse report = newExecutionReport().toBuilder()
            .setExecTypeValue(256)
            .build();
        Exception ex = assertThrows(FailureRequeueException.class, () -> orderService.onExecutionReport(report));
        assertThat(ex.getMessage()).isEqualTo("Received ExecType.256 when in unrecognized state 0");
    }

    @Test
    @Disabled //TODO tkow - to enable when onOrderNotDelivered is implemented
    void whenOrderNotDeliveredThenRequeue() {
        Exception ex = assertThrows(FailureRequeueException.class, () -> orderService.onOrderNotDelivered(order));
        assertThat(ex.getMessage()).isEqualTo("Received OrderNotDelivered when in unrecognized state value=0");
    }

    @Test
    @Disabled //TODO tkow - to enable when onCancelNotDelivered is implemented
    void whenCancelNotDeliveredThenRequeue() {
        OemsRequest venueCancel = OemsRequest.newBuilder()
            .setRequestType(OemsRequest.OemsRequestType.CANCEL)
            .setOrderId(order.getOrderId())
            .setMetadata(Metadata.newBuilder()
                .setRequestId(requestId)
                .build())
            .build();
        Exception ex = assertThrows(FailureRequeueException.class, () -> orderService.onCancelNotDelivered(venueCancel));
        assertThat(ex.getMessage()).isEqualTo("Received CancelNotDelivered when in unrecognized state value=0");
    }

    @Test
    void whenConvertedToVenueOrderStatusThenReturnsStatusUnspecified() {
        OrderStatus orderStatus = StatusUnrecognized.create(127);
        assertThat(orderStatus.toOemsOrderStatus()).isEqualTo(ORDER_STATUS_UNSPECIFIED);
    }

    @Test
    void equalsOnlyWhenVenueOrderStatusEquals() {
        assertThat(StatusUnrecognized.create(500)).isEqualTo(StatusUnrecognized.create(500));
        assertThat(StatusUnrecognized.create(500)).isNotEqualTo(StatusUnrecognized.create(501));
    }

    @Test
    void toStringReturnsBothPrecedenceAndVenueOrderStatus() {
        OrderStatus orderStatus = StatusUnrecognized.create(303);
        assertThat(orderStatus).hasToString("StatusUnrecognized(303).100");
    }

    @Test
    void compareToComparesVenueOrderStatus() {
        assertThat(StatusUnrecognized.create(128)).isLessThan(StatusUnrecognized.create(129));
    }

    @Test
    void statusUnrecognizedIsGreaterThanOtherStates() {
        assertThat(StatusUnrecognized.create(1)).isGreaterThan(StatusPendingCancel.create());
    }
}
