package io.wyden.executionengine.infrastructure.rabbit;

import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.destination.OemsExchange;
import io.wyden.published.marketdata.MarketDataRequest;
import org.springframework.stereotype.Component;

@Component
public class MarketDataDestinations {

    private final RabbitExchange<MarketDataRequest> marketDataRequestExchange;

    public MarketDataDestinations(RabbitIntegrator rabbitIntegrator) {
        this.marketDataRequestExchange = OemsExchange.MarketData.declareMarketDataRequest(rabbitIntegrator);
    }

    public RabbitExchange<MarketDataRequest> getMarketDataRequestExchange() {
        return marketDataRequestExchange;
    }
}
