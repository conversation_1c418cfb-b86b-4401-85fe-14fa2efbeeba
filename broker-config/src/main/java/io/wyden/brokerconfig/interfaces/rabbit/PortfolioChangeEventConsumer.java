package io.wyden.brokerconfig.interfaces.rabbit;

import com.rabbitmq.client.AMQP;
import io.wyden.brokerconfig.application.PortfolioGroupStorageService;
import io.wyden.brokerconfig.application.PortfolioStorageService;
import io.wyden.cloudutils.rabbitmq.ConsumptionResult;
import io.wyden.cloudutils.rabbitmq.RabbitExchange;
import io.wyden.cloudutils.rabbitmq.RabbitIntegrator;
import io.wyden.cloudutils.rabbitmq.queue.MessageConsumer;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueue;
import io.wyden.cloudutils.rabbitmq.queue.RabbitQueueBuilder;
import io.wyden.published.referencedata.Portfolio;
import io.wyden.published.referencedata.PortfolioChangeEvent;
import io.wyden.published.referencedata.PortfolioChangeEventType;
import jakarta.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

import static io.wyden.cloudutils.rabbitmq.ConsumptionResult.consumed;

@Component
public class PortfolioChangeEventConsumer implements MessageConsumer<PortfolioChangeEvent> {

    public static final Logger LOGGER = LoggerFactory.getLogger(PortfolioChangeEventConsumer.class);
    private final RabbitIntegrator rabbitIntegrator;
    private final RabbitExchange<PortfolioChangeEvent> portfolioChangeEventExchange;
    private final PortfolioGroupStorageService portfolioGroupStorageService;
    private final PortfolioStorageService portfolioStorageService;
    private final String queueName;
    private final String consumerName;
    private static final List<PortfolioChangeEventType> ALLOWED_CHANGE_EVENT_TYPES = List.of(
        PortfolioChangeEventType.PORTFOLIO_CHANGE_EVENT_TYPE_CREATED,
        PortfolioChangeEventType.PORTFOLIO_CHANGE_EVENT_TYPE_UPDATED);

    public PortfolioChangeEventConsumer(RabbitIntegrator rabbitIntegrator,
                                        RabbitExchange<PortfolioChangeEvent> portfolioChangeEventExchange,
                                        PortfolioGroupStorageService portfolioGroupStorageService,
                                        PortfolioStorageService portfolioStorageService,
                                        @Value("${rabbitmq.reference-data-broker-config-service-queue}") String queueName,
                                        @Value("${spring.application.name}") String consumerName) {
        this.rabbitIntegrator = rabbitIntegrator;
        this.portfolioChangeEventExchange = portfolioChangeEventExchange;
        this.portfolioGroupStorageService = portfolioGroupStorageService;
        this.portfolioStorageService = portfolioStorageService;
        this.queueName = queueName;
        this.consumerName = consumerName;
    }

    @PostConstruct
    void init() {
        declareQueue();
    }

    @Override
    public ConsumptionResult consume(PortfolioChangeEvent portfolioChangeEvent, AMQP.BasicProperties basicProperties) {
        try {
            LOGGER.info("Consuming PortfolioChangeEvent: \n{}", portfolioChangeEvent);
            if (shouldSkip(portfolioChangeEvent)) {
                LOGGER.info("PortfolioChangeEvent will not be processed because of its type: {}\n", portfolioChangeEvent);
                return ConsumptionResult.consumed();
            }
            Portfolio portfolio = portfolioChangeEvent.getPortfolio();
            portfolioGroupStorageService.findOrCreateGroupFor(portfolio)
                .ifPresent(portfolioGroupConfig -> {
                    LOGGER.info("Group: ({}) was found for PortfolioChangeEvent: \n{}", portfolioGroupConfig.getId(), portfolioGroupConfig);
                    portfolioStorageService.assignGroup(portfolio, portfolioGroupConfig.getId())
                        .ifPresent(portfolioConfig -> LOGGER.info("Group: ({}) was assigned to portfolio: \n{}", portfolioGroupConfig.getId(), portfolioConfig));
                });
        } catch (Exception e) {
            LOGGER.error("Exception during consuming PortfolioChangeEvent: %s".formatted(portfolioChangeEvent), e);
        }
        return consumed();
    }

    private void declareQueue() {
        RabbitQueue<PortfolioChangeEvent> queue = new RabbitQueueBuilder<PortfolioChangeEvent>(rabbitIntegrator)
            .setQueueName(queueName)
            .setConsumerName(consumerName)
            .declare();

        queue.bindWithRoutingKey(portfolioChangeEventExchange, StringUtils.EMPTY);
        LOGGER.info("Binding exchange {} and queue {}", portfolioChangeEventExchange, queue);
        queue.attachConsumer(PortfolioChangeEvent.parser(), this);
    }

    private boolean shouldSkip(PortfolioChangeEvent portfolioChangeEvent) {
        return !ALLOWED_CHANGE_EVENT_TYPES.contains(portfolioChangeEvent.getPortfolioChangeEventType());
    }
}
